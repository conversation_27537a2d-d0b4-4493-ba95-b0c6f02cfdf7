#!/usr/bin/env python3
"""
Exemplo de uso do PersonaCreator
Este script demonstra como usar o sistema passo a passo
"""

from main import PersonaCreatorOrchestrator
from loguru import logger
import sys

def exemplo_workflow_completo():
    """Exemplo de execução do workflow completo"""
    print("🚀 EXEMPLO: Workflow Completo")
    print("="*50)
    
    # URL de exemplo (substitua por um canal real)
    channel_url = "https://www.youtube.com/@canalexemplo"
    
    # Inicializa o orquestrador
    orchestrator = PersonaCreatorOrchestrator()
    
    # Executa workflow completo
    print(f"Analisando canal: {channel_url}")
    
    result = orchestrator.run_full_workflow(
        channel_url=channel_url,
        max_videos=10,  # Limita para exemplo
        selected_tags=['filho', 'filha', 'familia'],
        use_ai=True
    )
    
    if result['success']:
        print("\n✅ Workflow concluído com sucesso!")
        summary = result['final_summary']
        print(f"📊 Resumo:")
        print(f"   • Canal ID: {summary['channel_id']}")
        print(f"   • Vídeos analisados: {summary['total_videos_analyzed']}")
        print(f"   • Vídeos relevantes: {summary['relevant_videos']}")
        print(f"   • Comentários extraídos: {summary['total_comments_extracted']}")
        print(f"   • Confiança da persona: {summary['persona_confidence']:.2%}")
        print(f"   • Relatório: {summary['persona_report_file']}")
    else:
        print("❌ Workflow falhou!")
        print(f"Erros: {', '.join(result['errors'])}")

def exemplo_workflow_por_etapas():
    """Exemplo de execução do workflow por etapas"""
    print("\n🔧 EXEMPLO: Workflow por Etapas")
    print("="*50)
    
    channel_url = "https://www.youtube.com/@canalexemplo"
    orchestrator = PersonaCreatorOrchestrator()
    
    # Etapa 1: Curadoria
    print("\n1️⃣ Executando curadoria...")
    result1 = orchestrator.run_partial_workflow(
        step="curation",
        channel_url=channel_url
    )
    
    if not result1['success']:
        print("❌ Curadoria falhou!")
        return
    
    channel_id = result1['channel_id']
    print(f"✅ Curadoria concluída! Canal ID: {channel_id}")
    
    # Etapa 2: Priorização
    print("\n2️⃣ Executando priorização...")
    result2 = orchestrator.run_partial_workflow(
        step="prioritization",
        channel_id=channel_id
    )
    
    if result2['success']:
        print("✅ Priorização concluída!")
    else:
        print("❌ Priorização falhou!")
        return
    
    # Etapa 3: Extração de comentários
    print("\n3️⃣ Executando extração de comentários...")
    result3 = orchestrator.run_partial_workflow(
        step="extraction",
        channel_id=channel_id,
        max_videos=5
    )
    
    if result3['success']:
        print(f"✅ Extração concluída! {result3['total_comments_extracted']} comentários")
    else:
        print("❌ Extração falhou!")
        return
    
    # Etapa 4: Classificação
    print("\n4️⃣ Executando classificação...")
    result4 = orchestrator.run_partial_workflow(
        step="classification",
        channel_id=channel_id,
        use_ai=True
    )
    
    if result4['success']:
        print("✅ Classificação concluída!")
    else:
        print("❌ Classificação falhou!")
        return
    
    # Etapa 5: Análise de sentimentos
    print("\n5️⃣ Executando análise de sentimentos...")
    result5 = orchestrator.run_partial_workflow(
        step="sentiment_analysis",
        channel_id=channel_id,
        selected_tags=['filho', 'filha'],
        min_confidence=0.5
    )
    
    if result5['success']:
        print("✅ Análise de sentimentos concluída!")
    else:
        print("❌ Análise de sentimentos falhou!")
        return
    
    # Etapa 6: Geração de persona
    print("\n6️⃣ Executando geração de persona...")
    result6 = orchestrator.run_partial_workflow(
        step="persona_generation",
        channel_id=channel_id
    )
    
    if result6['success']:
        print("✅ Persona gerada com sucesso!")
        print(f"📄 Relatório: {result6['report_file']}")
        print(f"🎯 Confiança: {result6['confidence_score']:.2%}")
    else:
        print("❌ Geração de persona falhou!")

def exemplo_verificar_status():
    """Exemplo de como verificar o status de um workflow"""
    print("\n📊 EXEMPLO: Verificar Status")
    print("="*50)
    
    orchestrator = PersonaCreatorOrchestrator()
    
    # Verifica status de um canal (substitua por ID real)
    channel_id = 1
    
    status = orchestrator.get_workflow_status(channel_id)
    
    print(f"Status do Canal {channel_id}:")
    print(f"  📹 Vídeos: {status['videos']['total']} total, {status['videos']['relevant']} relevantes")
    print(f"  💬 Comentários: {status['comments']['total_comments']}")
    print(f"  🏷️  Comentários com tags: {status['tags']['tagged_comments']}")
    print(f"  🧠 Comentários analisados: {status['analyses']['analyzed_comments']}")
    print(f"  👤 Personas geradas: {status['personas']['personas']}")
    print(f"  ✅ Workflow completo: {'Sim' if status['workflow_complete'] else 'Não'}")

def exemplo_uso_direto_agentes():
    """Exemplo de uso direto dos agentes"""
    print("\n🤖 EXEMPLO: Uso Direto dos Agentes")
    print("="*50)
    
    from database import DatabaseManager
    from ai_utils import LLaMAClient
    from agents.content_curator import ContentCuratorAgent
    
    # Inicializa componentes
    db = DatabaseManager()
    llama = LLaMAClient()
    
    # Usa agente diretamente
    curator = ContentCuratorAgent(db, llama)
    
    # Exemplo: analisa relevância de um título
    video_exemplo = {
        'title': 'Como educar filhos com amor e disciplina',
        'description': 'Dicas para pais sobre educação positiva'
    }
    
    analysis = curator.analyze_video_relevance(video_exemplo)
    
    print("Análise de relevância:")
    print(f"  📝 Título: {video_exemplo['title']}")
    print(f"  ✅ Relevante: {analysis['is_relevant']}")
    print(f"  📊 Score: {analysis['relevance_score']:.2f}")
    print(f"  🔍 Contexto: {analysis['context_analysis']}")

def exemplo_configuracoes_personalizadas():
    """Exemplo de como usar configurações personalizadas"""
    print("\n⚙️  EXEMPLO: Configurações Personalizadas")
    print("="*50)
    
    # Exemplo de tags personalizadas
    tags_personalizadas = [
        'adolescente', 'teenager', 'puberdade', 
        'escola', 'bullying', 'ansiedade'
    ]
    
    # Exemplo de workflow com configurações específicas
    orchestrator = PersonaCreatorOrchestrator()
    
    print("Configurações personalizadas:")
    print(f"  🏷️  Tags: {', '.join(tags_personalizadas)}")
    print(f"  📹 Max vídeos: 15")
    print(f"  🤖 IA habilitada: Sim")
    print(f"  📊 Confiança mínima: 0.6")
    
    # Simulação (não executa realmente)
    print("\n💡 Para executar com essas configurações:")
    print("python main.py --channel-url 'URL' --max-videos 15 --tags adolescente teenager puberdade escola")

def main():
    """Menu principal de exemplos"""
    print("🎯 EXEMPLOS DE USO DO PERSONACREATOR")
    print("="*60)
    
    exemplos = [
        ("1", "Workflow Completo", exemplo_workflow_completo),
        ("2", "Workflow por Etapas", exemplo_workflow_por_etapas),
        ("3", "Verificar Status", exemplo_verificar_status),
        ("4", "Uso Direto dos Agentes", exemplo_uso_direto_agentes),
        ("5", "Configurações Personalizadas", exemplo_configuracoes_personalizadas),
    ]
    
    print("Escolha um exemplo para executar:")
    for num, nome, _ in exemplos:
        print(f"  {num}. {nome}")
    print("  0. Sair")
    
    while True:
        try:
            escolha = input("\nDigite o número do exemplo (0 para sair): ").strip()
            
            if escolha == "0":
                print("👋 Até logo!")
                break
            
            exemplo_encontrado = False
            for num, nome, func in exemplos:
                if escolha == num:
                    print(f"\n🚀 Executando: {nome}")
                    try:
                        func()
                    except Exception as e:
                        print(f"❌ Erro no exemplo: {e}")
                        logger.error(f"Erro no exemplo {nome}: {e}")
                    exemplo_encontrado = True
                    break
            
            if not exemplo_encontrado:
                print("❌ Opção inválida! Digite um número de 0 a 5.")
                
        except KeyboardInterrupt:
            print("\n\n👋 Saindo...")
            break
        except Exception as e:
            print(f"❌ Erro: {e}")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 Programa interrompido pelo usuário")
        sys.exit(0)
