"""
Agente 1 - Curador de Conteúdo
Responsável por listar vídeos do canal e analisar títulos para relevância
"""
import json
import re
from datetime import datetime
from typing import List, Dict, Any, Optional
from pathlib import Path
import yt_dlp
from loguru import logger
from crewai import Agent, Task, Crew

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from database import DatabaseManager
from ai_utils import LLaMAClient, AnalysisPrompts
from config import TARGET_CONTEXT_KEYWORDS, MAX_VIDEOS_PER_CHANNEL, OUTPUT_DIR


class ContentCuratorAgent:
    """Agente curador de conteúdo do YouTube"""
    
    def __init__(self, db_manager: DatabaseManager, llama_client: LLaMAClient):
        self.db = db_manager
        self.llama = llama_client
        self.ydl_opts = {
            'quiet': True,
            'no_warnings': True,
            'extract_flat': True,
            'playlistend': MAX_VIDEOS_PER_CHANNEL,
        }
    
    def extract_channel_videos(self, channel_url: str) -> List[Dict[str, Any]]:
        """Extrai lista de vídeos de um canal do YouTube"""
        logger.info(f"Extraindo vídeos do canal: {channel_url}")
        
        videos = []
        try:
            with yt_dlp.YoutubeDL(self.ydl_opts) as ydl:
                # Extrai informações do canal
                channel_info = ydl.extract_info(channel_url, download=False)
                
                if 'entries' in channel_info:
                    for entry in channel_info['entries']:
                        if entry and 'id' in entry:
                            # Extrai informações detalhadas de cada vídeo
                            video_info = self._extract_video_details(entry['id'])
                            if video_info:
                                videos.append(video_info)
                
                logger.info(f"Extraídos {len(videos)} vídeos do canal")
                return videos
                
        except Exception as e:
            logger.error(f"Erro ao extrair vídeos do canal: {e}")
            return []
    
    def _extract_video_details(self, video_id: str) -> Optional[Dict[str, Any]]:
        """Extrai detalhes específicos de um vídeo"""
        try:
            video_url = f"https://www.youtube.com/watch?v={video_id}"
            
            ydl_opts_detailed = {
                'quiet': True,
                'no_warnings': True,
            }
            
            with yt_dlp.YoutubeDL(ydl_opts_detailed) as ydl:
                info = ydl.extract_info(video_url, download=False)
                
                return {
                    'video_id': video_id,
                    'title': info.get('title', ''),
                    'description': info.get('description', ''),
                    'url': video_url,
                    'view_count': info.get('view_count', 0),
                    'like_count': info.get('like_count', 0),
                    'comment_count': info.get('comment_count', 0),
                    'upload_date': info.get('upload_date', ''),
                    'duration': info.get('duration_string', ''),
                    'uploader': info.get('uploader', ''),
                    'channel_id': info.get('channel_id', ''),
                    'channel_url': info.get('channel_url', '')
                }
                
        except Exception as e:
            logger.warning(f"Erro ao extrair detalhes do vídeo {video_id}: {e}")
            return None
    
    def analyze_video_relevance(self, video: Dict[str, Any]) -> Dict[str, Any]:
        """Analisa se um vídeo é relevante para o contexto-alvo"""
        title = video.get('title', '')
        
        # Usa IA para analisar relevância
        prompt = AnalysisPrompts.video_title_relevance(title, TARGET_CONTEXT_KEYWORDS)
        response = self.llama.generate_response(prompt, temperature=0.3)
        
        try:
            analysis = json.loads(response)
            return {
                'is_relevant': analysis.get('relevante', False),
                'relevance_score': analysis.get('score', 0.0),
                'context_analysis': analysis.get('contexto_identificado', ''),
                'keywords_found': analysis.get('palavras_chave_encontradas', [])
            }
        except Exception as e:
            logger.warning(f"Erro ao analisar relevância do vídeo '{title}': {e}")
            # Fallback: análise simples baseada em palavras-chave
            return self._simple_relevance_analysis(title)
    
    def _simple_relevance_analysis(self, title: str) -> Dict[str, Any]:
        """Análise simples de relevância baseada em palavras-chave"""
        title_lower = title.lower()
        found_keywords = []
        
        for keyword in TARGET_CONTEXT_KEYWORDS:
            if keyword.lower() in title_lower:
                found_keywords.append(keyword)
        
        relevance_score = len(found_keywords) / len(TARGET_CONTEXT_KEYWORDS)
        is_relevant = relevance_score > 0.1  # Pelo menos 10% das palavras-chave
        
        return {
            'is_relevant': is_relevant,
            'relevance_score': relevance_score,
            'context_analysis': f"Encontradas palavras-chave: {', '.join(found_keywords)}" if found_keywords else "Nenhuma palavra-chave relevante encontrada",
            'keywords_found': found_keywords
        }
    
    def save_videos_to_database(self, channel_url: str, videos: List[Dict[str, Any]]) -> int:
        """Salva vídeos no banco de dados"""
        # Insere ou busca canal
        channel_name = videos[0].get('uploader', '') if videos else ''
        channel_id_yt = videos[0].get('channel_id', '') if videos else ''
        
        channel_id = self.db.insert_channel(channel_url, channel_name, channel_id_yt)
        
        relevant_count = 0
        for video in videos:
            # Analisa relevância
            analysis = self.analyze_video_relevance(video)
            
            # Adiciona dados do canal
            video['channel_id'] = channel_id
            
            # Salva vídeo no banco
            video_db_id = self.db.insert_video(video)
            
            # Atualiza análise de relevância
            self.db.update_video_analysis(
                video['video_id'],
                analysis['relevance_score'],
                analysis['is_relevant'],
                analysis['context_analysis']
            )
            
            if analysis['is_relevant']:
                relevant_count += 1
                logger.info(f"Vídeo relevante encontrado: {video['title']}")
        
        logger.info(f"Salvos {len(videos)} vídeos, {relevant_count} relevantes")
        return channel_id
    
    def export_relevant_videos(self, channel_id: int, output_file: str = None) -> str:
        """Exporta vídeos relevantes para arquivo JSON"""
        if not output_file:
            output_file = OUTPUT_DIR / "relevant_videos.json"
        
        relevant_videos = self.db.get_relevant_videos(channel_id)
        
        export_data = {
            'channel_id': channel_id,
            'total_relevant_videos': len(relevant_videos),
            'extraction_date': str(datetime.now()),
            'videos': relevant_videos
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Vídeos relevantes exportados para: {output_file}")
        return str(output_file)
    
    def run_curation_workflow(self, channel_url: str) -> Dict[str, Any]:
        """Executa o workflow completo de curadoria"""
        logger.info("Iniciando workflow de curadoria de conteúdo")
        
        # 1. Extrai vídeos do canal
        videos = self.extract_channel_videos(channel_url)
        if not videos:
            return {'success': False, 'error': 'Nenhum vídeo encontrado'}
        
        # 2. Salva no banco de dados com análise de relevância
        channel_id = self.save_videos_to_database(channel_url, videos)
        
        # 3. Exporta vídeos relevantes
        export_file = self.export_relevant_videos(channel_id)
        
        # 4. Estatísticas finais
        relevant_videos = self.db.get_relevant_videos(channel_id)
        
        result = {
            'success': True,
            'channel_id': channel_id,
            'total_videos': len(videos),
            'relevant_videos': len(relevant_videos),
            'relevance_rate': len(relevant_videos) / len(videos) if videos else 0,
            'export_file': export_file,
            'top_relevant_videos': [
                {
                    'title': v['title'],
                    'relevance_score': v['context_relevance_score'],
                    'url': v['url']
                }
                for v in relevant_videos[:5]
            ]
        }
        
        logger.info(f"Curadoria concluída: {result['relevant_videos']}/{result['total_videos']} vídeos relevantes")
        return result


def create_content_curator_crew_agent() -> Agent:
    """Cria agente CrewAI para curadoria de conteúdo"""
    return Agent(
        role='Curador de Conteúdo YouTube',
        goal='Identificar e catalogar vídeos relevantes sobre parentalidade e família',
        backstory="""Você é um especialista em análise de conteúdo do YouTube com foco em 
        parentalidade, educação infantil e dinâmicas familiares. Sua missão é identificar 
        vídeos que contenham discussões valiosas sobre a experiência de ser pai ou mãe.""",
        verbose=True,
        allow_delegation=False
    )


def create_content_analysis_task(agent: Agent, channel_url: str) -> Task:
    """Cria task CrewAI para análise de conteúdo"""
    return Task(
        description=f"""
        Analise o canal do YouTube: {channel_url}
        
        Suas responsabilidades:
        1. Extrair lista completa de vídeos do canal
        2. Analisar títulos para identificar relevância com parentalidade
        3. Classificar vídeos por relevância ao contexto familiar
        4. Salvar resultados estruturados para próximas etapas
        
        Foque em vídeos que discutam:
        - Desafios da parentalidade
        - Educação de filhos
        - Dinâmicas familiares
        - Desenvolvimento infantil
        - Experiências de pais e mães
        """,
        agent=agent,
        expected_output="Relatório com vídeos categorizados por relevância e análise contextual"
    )
