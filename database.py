"""
Módulo de gerenciamento do banco de dados SQLite
"""
import sqlite3
import json
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional
from loguru import logger
from config import DATABASE_PATH


class DatabaseManager:
    """Gerenciador do banco de dados SQLite"""
    
    def __init__(self, db_path: Path = DATABASE_PATH):
        self.db_path = db_path
        self.init_database()
    
    def get_connection(self) -> sqlite3.Connection:
        """Cria conexão com o banco de dados"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # Para acessar colunas por nome
        return conn
    
    def init_database(self):
        """Inicializa o banco de dados com as tabelas necessárias"""
        with self.get_connection() as conn:
            # Tabela de canais
            conn.execute("""
                CREATE TABLE IF NOT EXISTS channels (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    channel_url TEXT UNIQUE NOT NULL,
                    channel_name TEXT,
                    channel_id TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Tabela de vídeos
            conn.execute("""
                CREATE TABLE IF NOT EXISTS videos (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    channel_id INTEGER,
                    video_id TEXT UNIQUE NOT NULL,
                    title TEXT NOT NULL,
                    description TEXT,
                    url TEXT NOT NULL,
                    view_count INTEGER DEFAULT 0,
                    like_count INTEGER DEFAULT 0,
                    comment_count INTEGER DEFAULT 0,
                    upload_date TEXT,
                    duration TEXT,
                    context_relevance_score REAL DEFAULT 0,
                    is_relevant BOOLEAN DEFAULT 0,
                    context_analysis TEXT,
                    priority_score REAL DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (channel_id) REFERENCES channels (id)
                )
            """)
            
            # Tabela de comentários
            conn.execute("""
                CREATE TABLE IF NOT EXISTS comments (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    video_id INTEGER,
                    comment_id TEXT UNIQUE NOT NULL,
                    author TEXT,
                    text TEXT NOT NULL,
                    like_count INTEGER DEFAULT 0,
                    reply_count INTEGER DEFAULT 0,
                    published_at TEXT,
                    is_reply BOOLEAN DEFAULT 0,
                    parent_comment_id TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (video_id) REFERENCES videos (id)
                )
            """)
            
            # Tabela de tags de comentários
            conn.execute("""
                CREATE TABLE IF NOT EXISTS comment_tags (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    comment_id INTEGER,
                    tag_name TEXT NOT NULL,
                    tag_category TEXT,
                    confidence_score REAL DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (comment_id) REFERENCES comments (id)
                )
            """)
            
            # Tabela de análises de sentimentos/dores/desejos
            conn.execute("""
                CREATE TABLE IF NOT EXISTS comment_analysis (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    comment_id INTEGER,
                    analysis_type TEXT NOT NULL, -- 'dores', 'desejos', 'sonhos', etc.
                    extracted_content TEXT,
                    confidence_score REAL DEFAULT 0,
                    keywords TEXT, -- JSON array de palavras-chave
                    sentiment_score REAL DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (comment_id) REFERENCES comments (id)
                )
            """)
            
            # Tabela de personas geradas
            conn.execute("""
                CREATE TABLE IF NOT EXISTS personas (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    channel_id INTEGER,
                    persona_name TEXT NOT NULL,
                    demographic_data TEXT, -- JSON
                    pain_points TEXT, -- JSON
                    desires TEXT, -- JSON
                    dreams TEXT, -- JSON
                    frustrations TEXT, -- JSON
                    magic_words TEXT, -- JSON
                    business_opportunities TEXT, -- JSON
                    persona_description TEXT,
                    confidence_score REAL DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (channel_id) REFERENCES channels (id)
                )
            """)
            
            # Índices para melhor performance
            conn.execute("CREATE INDEX IF NOT EXISTS idx_videos_channel_id ON videos(channel_id)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_comments_video_id ON comments(video_id)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_comment_tags_comment_id ON comment_tags(comment_id)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_comment_analysis_comment_id ON comment_analysis(comment_id)")
            
            conn.commit()
            logger.info("Banco de dados inicializado com sucesso")
    
    def insert_channel(self, channel_url: str, channel_name: str = None, channel_id: str = None) -> int:
        """Insere um canal no banco de dados"""
        with self.get_connection() as conn:
            cursor = conn.execute(
                "INSERT OR IGNORE INTO channels (channel_url, channel_name, channel_id) VALUES (?, ?, ?)",
                (channel_url, channel_name, channel_id)
            )
            if cursor.rowcount == 0:
                # Canal já existe, busca o ID
                cursor = conn.execute("SELECT id FROM channels WHERE channel_url = ?", (channel_url,))
                return cursor.fetchone()[0]
            return cursor.lastrowid
    
    def insert_video(self, video_data: Dict[str, Any]) -> int:
        """Insere um vídeo no banco de dados"""
        with self.get_connection() as conn:
            cursor = conn.execute("""
                INSERT OR REPLACE INTO videos 
                (channel_id, video_id, title, description, url, view_count, like_count, 
                 comment_count, upload_date, duration)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                video_data.get('channel_id'),
                video_data.get('video_id'),
                video_data.get('title'),
                video_data.get('description'),
                video_data.get('url'),
                video_data.get('view_count', 0),
                video_data.get('like_count', 0),
                video_data.get('comment_count', 0),
                video_data.get('upload_date'),
                video_data.get('duration')
            ))
            return cursor.lastrowid
    
    def update_video_analysis(self, video_id: str, relevance_score: float, 
                            is_relevant: bool, context_analysis: str, priority_score: float = 0):
        """Atualiza a análise de contexto de um vídeo"""
        with self.get_connection() as conn:
            conn.execute("""
                UPDATE videos 
                SET context_relevance_score = ?, is_relevant = ?, 
                    context_analysis = ?, priority_score = ?
                WHERE video_id = ?
            """, (relevance_score, is_relevant, context_analysis, priority_score, video_id))
    
    def insert_comment(self, comment_data: Dict[str, Any]) -> int:
        """Insere um comentário no banco de dados"""
        with self.get_connection() as conn:
            cursor = conn.execute("""
                INSERT OR REPLACE INTO comments 
                (video_id, comment_id, author, text, like_count, reply_count, 
                 published_at, is_reply, parent_comment_id)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                comment_data.get('video_id'),
                comment_data.get('comment_id'),
                comment_data.get('author'),
                comment_data.get('text'),
                comment_data.get('like_count', 0),
                comment_data.get('reply_count', 0),
                comment_data.get('published_at'),
                comment_data.get('is_reply', False),
                comment_data.get('parent_comment_id')
            ))
            return cursor.lastrowid
    
    def insert_comment_tag(self, comment_id: int, tag_name: str, 
                          tag_category: str, confidence_score: float = 0):
        """Insere uma tag para um comentário"""
        with self.get_connection() as conn:
            conn.execute("""
                INSERT INTO comment_tags (comment_id, tag_name, tag_category, confidence_score)
                VALUES (?, ?, ?, ?)
            """, (comment_id, tag_name, tag_category, confidence_score))
    
    def insert_comment_analysis(self, comment_id: int, analysis_type: str, 
                              extracted_content: str, confidence_score: float,
                              keywords: List[str], sentiment_score: float = 0):
        """Insere análise de um comentário"""
        with self.get_connection() as conn:
            conn.execute("""
                INSERT INTO comment_analysis 
                (comment_id, analysis_type, extracted_content, confidence_score, keywords, sentiment_score)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (comment_id, analysis_type, extracted_content, confidence_score, 
                  json.dumps(keywords), sentiment_score))
    
    def insert_persona(self, persona_data: Dict[str, Any]) -> int:
        """Insere uma persona gerada"""
        with self.get_connection() as conn:
            cursor = conn.execute("""
                INSERT INTO personas 
                (channel_id, persona_name, demographic_data, pain_points, desires, dreams,
                 frustrations, magic_words, business_opportunities, persona_description, confidence_score)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                persona_data.get('channel_id'),
                persona_data.get('persona_name'),
                json.dumps(persona_data.get('demographic_data', {})),
                json.dumps(persona_data.get('pain_points', [])),
                json.dumps(persona_data.get('desires', [])),
                json.dumps(persona_data.get('dreams', [])),
                json.dumps(persona_data.get('frustrations', [])),
                json.dumps(persona_data.get('magic_words', [])),
                json.dumps(persona_data.get('business_opportunities', [])),
                persona_data.get('persona_description'),
                persona_data.get('confidence_score', 0)
            ))
            return cursor.lastrowid
    
    def get_relevant_videos(self, channel_id: int, limit: int = None) -> List[Dict]:
        """Busca vídeos relevantes ordenados por prioridade"""
        with self.get_connection() as conn:
            query = """
                SELECT * FROM videos 
                WHERE channel_id = ? AND is_relevant = 1 
                ORDER BY priority_score DESC, context_relevance_score DESC
            """
            if limit:
                query += f" LIMIT {limit}"
            
            cursor = conn.execute(query, (channel_id,))
            return [dict(row) for row in cursor.fetchall()]
    
    def get_comments_by_tags(self, video_ids: List[int], tags: List[str]) -> List[Dict]:
        """Busca comentários que possuem determinadas tags"""
        with self.get_connection() as conn:
            placeholders = ','.join(['?' for _ in video_ids])
            tag_placeholders = ','.join(['?' for _ in tags])
            
            query = f"""
                SELECT DISTINCT c.*, ct.tag_name, ct.tag_category, ct.confidence_score as tag_confidence
                FROM comments c
                JOIN comment_tags ct ON c.id = ct.comment_id
                WHERE c.video_id IN ({placeholders}) 
                AND ct.tag_name IN ({tag_placeholders})
                ORDER BY c.like_count DESC, ct.confidence_score DESC
            """
            
            cursor = conn.execute(query, video_ids + tags)
            return [dict(row) for row in cursor.fetchall()]
    
    def get_analysis_summary(self, channel_id: int) -> Dict[str, List]:
        """Busca resumo de todas as análises para geração de persona"""
        with self.get_connection() as conn:
            query = """
                SELECT ca.analysis_type, ca.extracted_content, ca.confidence_score, ca.keywords
                FROM comment_analysis ca
                JOIN comments c ON ca.comment_id = c.id
                JOIN videos v ON c.video_id = v.id
                WHERE v.channel_id = ?
                ORDER BY ca.confidence_score DESC
            """
            
            cursor = conn.execute(query, (channel_id,))
            results = cursor.fetchall()
            
            summary = {}
            for row in results:
                analysis_type = row['analysis_type']
                if analysis_type not in summary:
                    summary[analysis_type] = []
                
                summary[analysis_type].append({
                    'content': row['extracted_content'],
                    'confidence': row['confidence_score'],
                    'keywords': json.loads(row['keywords']) if row['keywords'] else []
                })
            
            return summary
