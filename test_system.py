#!/usr/bin/env python3
"""
Script de teste para verificar se o sistema PersonaCreator está funcionando corretamente
"""

import sys
import traceback
from pathlib import Path

def test_imports():
    """Testa se todos os módulos podem ser importados"""
    print("🔍 Testando imports...")
    
    try:
        # Testa imports básicos
        import sqlite3
        import json
        import re
        from datetime import datetime
        print("  ✓ Módulos básicos do Python")
        
        # Testa dependências externas
        import yt_dlp
        print("  ✓ yt-dlp")
        
        from loguru import logger
        print("  ✓ loguru")
        
        try:
            from crewai import Agent, Task, Crew
            print("  ✓ crewai")
        except ImportError:
            print("  ⚠️  crewai não encontrado - instale com: pip install crewai")
        
        # Testa módulos do projeto
        from config import DATABASE_PATH, TARGET_CONTEXT_KEYWORDS
        print("  ✓ config")
        
        from database import DatabaseManager
        print("  ✓ database")
        
        from ai_utils import LLaMAClient
        print("  ✓ ai_utils")
        
        # Testa agentes
        from agents.content_curator import ContentCuratorAgent
        from agents.priority_analyzer import PriorityAnalyzerAgent
        from agents.comment_extractor import CommentExtractorAgent
        from agents.comment_classifier import CommentClassifierAgent
        from agents.sentiment_analyzer import SentimentAnalyzerAgent
        from agents.persona_generator import PersonaGeneratorAgent
        print("  ✓ Todos os agentes")
        
        print("✅ Todos os imports funcionando!")
        return True
        
    except Exception as e:
        print(f"❌ Erro no import: {e}")
        traceback.print_exc()
        return False

def test_database():
    """Testa se o banco de dados pode ser inicializado"""
    print("\n🗄️  Testando banco de dados...")
    
    try:
        from database import DatabaseManager
        
        db = DatabaseManager()
        print("  ✓ Banco de dados inicializado")
        
        # Testa inserção de canal de teste
        channel_id = db.insert_channel("https://test.com", "Canal Teste", "test123")
        print(f"  ✓ Canal de teste inserido (ID: {channel_id})")
        
        # Testa inserção de vídeo de teste
        video_data = {
            'channel_id': channel_id,
            'video_id': 'test_video_123',
            'title': 'Como educar filhos - Teste',
            'description': 'Vídeo de teste sobre educação',
            'url': 'https://youtube.com/watch?v=test123',
            'view_count': 1000,
            'like_count': 50,
            'comment_count': 25,
            'upload_date': '20240101',
            'duration': '10:30'
        }
        
        video_db_id = db.insert_video(video_data)
        print(f"  ✓ Vídeo de teste inserido (ID: {video_db_id})")
        
        # Testa busca de vídeos
        videos = db.get_relevant_videos(channel_id)
        print(f"  ✓ Busca de vídeos funcionando ({len(videos)} encontrados)")
        
        print("✅ Banco de dados funcionando!")
        return True
        
    except Exception as e:
        print(f"❌ Erro no banco de dados: {e}")
        traceback.print_exc()
        return False

def test_llama_connection():
    """Testa conexão com LLaMA via Ollama"""
    print("\n🤖 Testando conexão com LLaMA...")
    
    try:
        from ai_utils import LLaMAClient
        
        llama = LLaMAClient()
        print("  ✓ Cliente LLaMA inicializado")
        
        # Testa geração simples
        response = llama.generate_response(
            "Responda apenas 'OK' se você está funcionando.",
            temperature=0.1,
            max_tokens=10
        )
        
        if response and len(response.strip()) > 0:
            print(f"  ✓ LLaMA respondeu: '{response.strip()}'")
            print("✅ LLaMA funcionando!")
            return True
        else:
            print("  ⚠️  LLaMA não respondeu adequadamente")
            print("     Verifique se o Ollama está rodando: ollama serve")
            print("     E se o modelo está instalado: ollama pull llama3:8b-instruct")
            return False
        
    except Exception as e:
        print(f"❌ Erro na conexão com LLaMA: {e}")
        print("   Certifique-se de que:")
        print("   1. Ollama está instalado e rodando")
        print("   2. Modelo llama3:8b-instruct está baixado")
        print("   3. Ollama está acessível em http://localhost:11434")
        return False

def test_youtube_extraction():
    """Testa extração básica do YouTube"""
    print("\n📺 Testando extração do YouTube...")
    
    try:
        import yt_dlp
        
        # Testa com um vídeo público conhecido
        test_url = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"  # Rick Roll - sempre disponível
        
        ydl_opts = {
            'quiet': True,
            'no_warnings': True,
            'extract_flat': True,
        }
        
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            info = ydl.extract_info(test_url, download=False)
            
            if info and 'title' in info:
                print(f"  ✓ Extração funcionando - Título: {info['title'][:50]}...")
                print("✅ YouTube extraction funcionando!")
                return True
            else:
                print("  ⚠️  Não foi possível extrair informações do vídeo")
                return False
        
    except Exception as e:
        print(f"❌ Erro na extração do YouTube: {e}")
        print("   Isso pode ser normal se houver restrições de rede")
        return False

def test_agents_initialization():
    """Testa se todos os agentes podem ser inicializados"""
    print("\n🤖 Testando inicialização dos agentes...")
    
    try:
        from database import DatabaseManager
        from ai_utils import LLaMAClient
        from agents.content_curator import ContentCuratorAgent
        from agents.priority_analyzer import PriorityAnalyzerAgent
        from agents.comment_extractor import CommentExtractorAgent
        from agents.comment_classifier import CommentClassifierAgent
        from agents.sentiment_analyzer import SentimentAnalyzerAgent
        from agents.persona_generator import PersonaGeneratorAgent
        
        db = DatabaseManager()
        llama = LLaMAClient()
        
        # Inicializa todos os agentes
        content_curator = ContentCuratorAgent(db, llama)
        print("  ✓ ContentCuratorAgent")
        
        priority_analyzer = PriorityAnalyzerAgent(db, llama)
        print("  ✓ PriorityAnalyzerAgent")
        
        comment_extractor = CommentExtractorAgent(db)
        print("  ✓ CommentExtractorAgent")
        
        comment_classifier = CommentClassifierAgent(db, llama)
        print("  ✓ CommentClassifierAgent")
        
        sentiment_analyzer = SentimentAnalyzerAgent(db, llama)
        print("  ✓ SentimentAnalyzerAgent")
        
        persona_generator = PersonaGeneratorAgent(db, llama)
        print("  ✓ PersonaGeneratorAgent")
        
        print("✅ Todos os agentes inicializados!")
        return True
        
    except Exception as e:
        print(f"❌ Erro na inicialização dos agentes: {e}")
        traceback.print_exc()
        return False

def test_main_orchestrator():
    """Testa se o orquestrador principal pode ser inicializado"""
    print("\n🎭 Testando orquestrador principal...")
    
    try:
        from main import PersonaCreatorOrchestrator
        
        orchestrator = PersonaCreatorOrchestrator()
        print("  ✓ Orquestrador inicializado")
        
        # Testa status de um canal inexistente
        status = orchestrator.get_workflow_status(999)
        print("  ✓ Método get_workflow_status funcionando")
        
        print("✅ Orquestrador funcionando!")
        return True
        
    except Exception as e:
        print(f"❌ Erro no orquestrador: {e}")
        traceback.print_exc()
        return False

def main():
    """Executa todos os testes"""
    print("🧪 INICIANDO TESTES DO SISTEMA PERSONACREATOR\n")
    
    tests = [
        ("Imports", test_imports),
        ("Banco de Dados", test_database),
        ("Conexão LLaMA", test_llama_connection),
        ("Extração YouTube", test_youtube_extraction),
        ("Inicialização Agentes", test_agents_initialization),
        ("Orquestrador Principal", test_main_orchestrator),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Erro crítico no teste {test_name}: {e}")
            results.append((test_name, False))
    
    # Resumo final
    print("\n" + "="*50)
    print("📊 RESUMO DOS TESTES")
    print("="*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSOU" if result else "❌ FALHOU"
        print(f"{test_name:.<30} {status}")
        if result:
            passed += 1
    
    print(f"\nResultado: {passed}/{total} testes passaram")
    
    if passed == total:
        print("\n🎉 TODOS OS TESTES PASSARAM!")
        print("O sistema está pronto para uso!")
        print("\nPara começar, execute:")
        print('python main.py --channel-url "https://www.youtube.com/@seucanal"')
    else:
        print(f"\n⚠️  {total - passed} TESTE(S) FALHARAM")
        print("Verifique os erros acima antes de usar o sistema.")
        
        if not results[2][1]:  # LLaMA test failed
            print("\n💡 DICA: Para configurar o LLaMA:")
            print("1. Instale Ollama: curl -fsSL https://ollama.ai/install.sh | sh")
            print("2. Baixe o modelo: ollama pull llama3:8b-instruct")
            print("3. Inicie o serviço: ollama serve")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
