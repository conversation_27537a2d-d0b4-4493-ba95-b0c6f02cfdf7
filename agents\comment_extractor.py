"""
Agente 3 - Extrator de Comentários
Responsável por extrair comentários dos vídeos priorizados
"""
import json
import time
from datetime import datetime
from typing import List, Dict, Any, Optional
from pathlib import Path
import sys
sys.path.append(str(Path(__file__).parent.parent))

import yt_dlp
from loguru import logger
from crewai import Agent, Task

from database import DatabaseManager
from config import MAX_COMMENTS_PER_VIDEO, OUTPUT_DIR


class CommentExtractorAgent:
    """Agente extrator de comentários do YouTube"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
        self.ydl_opts = {
            'quiet': True,
            'no_warnings': True,
            'writesubtitles': False,
            'writeautomaticsub': False,
            'getcomments': True,
            'max_comments': MAX_COMMENTS_PER_VIDEO,
        }
    
    def extract_video_comments(self, video_url: str, video_id: str) -> List[Dict[str, Any]]:
        """Extrai comentários de um vídeo específico"""
        logger.info(f"Extraindo comentários do vídeo: {video_id}")
        
        comments = []
        try:
            with yt_dlp.YoutubeDL(self.ydl_opts) as ydl:
                info = ydl.extract_info(video_url, download=False)
                
                if 'comments' in info and info['comments']:
                    for comment_data in info['comments']:
                        comment = self._process_comment_data(comment_data)
                        if comment:
                            comments.append(comment)
                
                logger.info(f"Extraídos {len(comments)} comentários do vídeo {video_id}")
                
        except Exception as e:
            logger.error(f"Erro ao extrair comentários do vídeo {video_id}: {e}")
        
        return comments
    
    def _process_comment_data(self, comment_data: Dict) -> Optional[Dict[str, Any]]:
        """Processa dados brutos de um comentário"""
        try:
            # Filtra comentários muito curtos ou irrelevantes
            text = comment_data.get('text', '').strip()
            if len(text) < 10:  # Comentários muito curtos
                return None
            
            # Remove comentários que são apenas emojis ou caracteres especiais
            if not any(c.isalpha() for c in text):
                return None
            
            return {
                'comment_id': comment_data.get('id', ''),
                'author': comment_data.get('author', 'Anônimo'),
                'text': text,
                'like_count': comment_data.get('like_count', 0),
                'reply_count': comment_data.get('reply_count', 0),
                'published_at': comment_data.get('timestamp', ''),
                'is_reply': comment_data.get('parent', 'root') != 'root',
                'parent_comment_id': comment_data.get('parent', '') if comment_data.get('parent', 'root') != 'root' else None
            }
            
        except Exception as e:
            logger.warning(f"Erro ao processar comentário: {e}")
            return None
    
    def filter_relevant_comments(self, comments: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Filtra comentários relevantes para análise de parentalidade"""
        relevant_comments = []
        
        # Palavras-chave que indicam comentários sobre parentalidade
        parenting_keywords = [
            'filho', 'filha', 'criança', 'bebê', 'pai', 'mãe', 'família',
            'educação', 'escola', 'comportamento', 'disciplina', 'amor',
            'cuidado', 'desenvolvimento', 'crescimento', 'idade', 'anos',
            'meses', 'gravidez', 'nascimento', 'amamentação', 'sono',
            'alimentação', 'birra', 'obediência', 'carinho', 'proteção'
        ]
        
        for comment in comments:
            text_lower = comment['text'].lower()
            
            # Verifica se contém palavras-chave relevantes
            has_keywords = any(keyword in text_lower for keyword in parenting_keywords)
            
            # Prioriza comentários com mais engajamento
            has_engagement = comment['like_count'] > 0 or comment['reply_count'] > 0
            
            # Filtra comentários muito negativos ou spam
            spam_indicators = ['link', 'http', 'www.', 'compre', 'venda', 'promoção']
            is_spam = any(indicator in text_lower for indicator in spam_indicators)
            
            if has_keywords and not is_spam:
                # Adiciona score de relevância
                relevance_score = 0.0
                
                # Score baseado em palavras-chave
                keyword_count = sum(1 for keyword in parenting_keywords if keyword in text_lower)
                relevance_score += min(keyword_count * 0.1, 0.5)
                
                # Score baseado em engajamento
                if has_engagement:
                    relevance_score += 0.3
                
                # Score baseado no tamanho (comentários mais elaborados)
                if len(comment['text']) > 50:
                    relevance_score += 0.2
                
                comment['relevance_score'] = relevance_score
                relevant_comments.append(comment)
        
        # Ordena por relevância e engajamento
        relevant_comments.sort(
            key=lambda x: (x['relevance_score'], x['like_count'], len(x['text'])),
            reverse=True
        )
        
        logger.info(f"Filtrados {len(relevant_comments)} comentários relevantes de {len(comments)} totais")
        return relevant_comments
    
    def save_comments_to_database(self, video_db_id: int, comments: List[Dict[str, Any]]) -> int:
        """Salva comentários no banco de dados"""
        saved_count = 0
        
        for comment in comments:
            try:
                comment['video_id'] = video_db_id
                comment_db_id = self.db.insert_comment(comment)
                saved_count += 1
                
            except Exception as e:
                logger.warning(f"Erro ao salvar comentário: {e}")
        
        logger.info(f"Salvos {saved_count} comentários no banco de dados")
        return saved_count
    
    def extract_comments_from_prioritized_videos(self, channel_id: int, max_videos: int = 20) -> Dict[str, Any]:
        """Extrai comentários dos vídeos mais priorizados"""
        logger.info(f"Extraindo comentários dos vídeos priorizados do canal {channel_id}")
        
        # Busca vídeos priorizados
        prioritized_videos = self.db.get_relevant_videos(channel_id, limit=max_videos)
        
        if not prioritized_videos:
            return {'success': False, 'error': 'Nenhum vídeo priorizado encontrado'}
        
        extraction_results = []
        total_comments = 0
        
        for i, video in enumerate(prioritized_videos, 1):
            logger.info(f"Processando vídeo {i}/{len(prioritized_videos)}: {video['title']}")
            
            # Extrai comentários
            comments = self.extract_video_comments(video['url'], video['video_id'])
            
            if comments:
                # Filtra comentários relevantes
                relevant_comments = self.filter_relevant_comments(comments)
                
                # Salva no banco de dados
                saved_count = self.save_comments_to_database(video['id'], relevant_comments)
                
                extraction_results.append({
                    'video_id': video['video_id'],
                    'video_title': video['title'],
                    'total_comments_extracted': len(comments),
                    'relevant_comments': len(relevant_comments),
                    'comments_saved': saved_count
                })
                
                total_comments += saved_count
            
            # Pausa entre extrações para evitar rate limiting
            if i < len(prioritized_videos):
                time.sleep(2)
        
        result = {
            'success': True,
            'channel_id': channel_id,
            'videos_processed': len(prioritized_videos),
            'total_comments_extracted': total_comments,
            'extraction_results': extraction_results,
            'average_comments_per_video': total_comments / len(prioritized_videos) if prioritized_videos else 0
        }
        
        logger.info(f"Extração concluída: {total_comments} comentários de {len(prioritized_videos)} vídeos")
        return result
    
    def export_comments_summary(self, channel_id: int, output_file: str = None) -> str:
        """Exporta resumo dos comentários extraídos"""
        if not output_file:
            output_file = OUTPUT_DIR / "comments_summary.json"
        
        # Busca estatísticas dos comentários
        with self.db.get_connection() as conn:
            cursor = conn.execute("""
                SELECT 
                    v.video_id,
                    v.title as video_title,
                    COUNT(c.id) as comment_count,
                    AVG(c.like_count) as avg_likes,
                    MAX(c.like_count) as max_likes,
                    AVG(LENGTH(c.text)) as avg_comment_length
                FROM videos v
                LEFT JOIN comments c ON v.id = c.video_id
                WHERE v.channel_id = ? AND v.is_relevant = 1
                GROUP BY v.id, v.video_id, v.title
                ORDER BY comment_count DESC
            """, (channel_id,))
            
            video_stats = [dict(row) for row in cursor.fetchall()]
        
        # Busca comentários mais engajados
        with self.db.get_connection() as conn:
            cursor = conn.execute("""
                SELECT 
                    c.text,
                    c.author,
                    c.like_count,
                    v.title as video_title
                FROM comments c
                JOIN videos v ON c.video_id = v.id
                WHERE v.channel_id = ?
                ORDER BY c.like_count DESC
                LIMIT 20
            """, (channel_id,))
            
            top_comments = [dict(row) for row in cursor.fetchall()]
        
        export_data = {
            'channel_id': channel_id,
            'extraction_date': str(datetime.now()),
            'video_statistics': video_stats,
            'top_engaged_comments': top_comments,
            'total_videos_with_comments': len([v for v in video_stats if v['comment_count'] > 0]),
            'total_comments': sum(v['comment_count'] for v in video_stats)
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Resumo de comentários exportado para: {output_file}")
        return str(output_file)
    
    def run_extraction_workflow(self, channel_id: int, max_videos: int = 20) -> Dict[str, Any]:
        """Executa workflow completo de extração de comentários"""
        logger.info("Iniciando workflow de extração de comentários")
        
        # 1. Extrai comentários dos vídeos priorizados
        result = self.extract_comments_from_prioritized_videos(channel_id, max_videos)
        
        if not result['success']:
            return result
        
        # 2. Exporta resumo
        export_file = self.export_comments_summary(channel_id)
        result['export_file'] = export_file
        
        logger.info("Workflow de extração de comentários concluído")
        return result


def create_comment_extractor_crew_agent() -> Agent:
    """Cria agente CrewAI para extração de comentários"""
    return Agent(
        role='Extrator de Comentários YouTube',
        goal='Extrair e filtrar comentários relevantes sobre parentalidade dos vídeos priorizados',
        backstory="""Você é um especialista em extração e filtragem de dados do YouTube 
        com foco em comentários sobre parentalidade. Sua expertise está em identificar 
        comentários que contêm insights valiosos sobre as experiências, desafios e 
        sentimentos de pais e mães, filtrando ruído e conteúdo irrelevante.""",
        verbose=True,
        allow_delegation=False
    )


def create_comment_extraction_task(agent: Agent, channel_id: int, max_videos: int = 20) -> Task:
    """Cria task CrewAI para extração de comentários"""
    return Task(
        description=f"""
        Extraia comentários dos vídeos mais priorizados do canal ID: {channel_id}
        
        Suas responsabilidades:
        1. Extrair comentários dos top {max_videos} vídeos priorizados
        2. Filtrar comentários relevantes para parentalidade
        3. Remover spam, comentários muito curtos ou irrelevantes
        4. Priorizar comentários com maior engajamento
        5. Salvar comentários estruturados para análise posterior
        
        Critérios de filtragem:
        - Comentários que mencionam filhos, família, parentalidade
        - Comentários com experiências pessoais
        - Comentários que geram discussão (likes, respostas)
        - Comentários elaborados (não apenas emojis ou palavras soltas)
        - Remover spam e conteúdo promocional
        """,
        agent=agent,
        expected_output="Base de dados estruturada com comentários relevantes filtrados e categorizados"
    )
