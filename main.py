#!/usr/bin/env python3
"""
PersonaCreator - <PERSON><PERSON>t Principal
<PERSON><PERSON><PERSON> de personas baseado em comentários do YouTube

Uso:
    python main.py --channel-url "https://www.youtube.com/@canal" [opções]
"""

import argparse
import sys
from pathlib import Path
from typing import List, Dict, Any
from loguru import logger

# Configuração de logging
from config import LOG_LEVEL, LOG_FORMAT, LOGS_DIR
logger.remove()
logger.add(sys.stderr, level=LOG_LEVEL, format=LOG_FORMAT)
logger.add(LOGS_DIR / "persona_creator.log", level="DEBUG", format=LOG_FORMAT, rotation="10 MB")

# Imports dos módulos
from database import DatabaseManager
from ai_utils import LLaMAClient
from agents.content_curator import ContentCuratorAgent
from agents.priority_analyzer import PriorityAnalyzerAgent
from agents.comment_extractor import CommentExtractorAgent
from agents.comment_classifier import CommentClassifierAgent
from agents.sentiment_analyzer import SentimentAnalyzerAgent
from agents.persona_generator import PersonaGeneratorAgent


class PersonaCreatorOrchestrator:
    """Orquestrador principal do sistema PersonaCreator"""
    
    def __init__(self):
        self.db = DatabaseManager()
        self.llama = LLaMAClient()
        
        # Inicializa agentes
        self.content_curator = ContentCuratorAgent(self.db, self.llama)
        self.priority_analyzer = PriorityAnalyzerAgent(self.db, self.llama)
        self.comment_extractor = CommentExtractorAgent(self.db)
        self.comment_classifier = CommentClassifierAgent(self.db, self.llama)
        self.sentiment_analyzer = SentimentAnalyzerAgent(self.db, self.llama)
        self.persona_generator = PersonaGeneratorAgent(self.db, self.llama)
        
        logger.info("PersonaCreator inicializado com sucesso")
    
    def run_full_workflow(self, channel_url: str, max_videos: int = 20, 
                         selected_tags: List[str] = None, use_ai: bool = True) -> Dict[str, Any]:
        """Executa workflow completo de análise de persona"""
        logger.info(f"Iniciando workflow completo para canal: {channel_url}")
        
        if selected_tags is None:
            selected_tags = ['filho', 'filha', 'familia', 'educacao']
        
        workflow_results = {
            'channel_url': channel_url,
            'workflow_steps': {},
            'success': True,
            'errors': []
        }
        
        try:
            # Etapa 1: Curadoria de Conteúdo
            logger.info("=== ETAPA 1: CURADORIA DE CONTEÚDO ===")
            curation_result = self.content_curator.run_curation_workflow(channel_url)
            workflow_results['workflow_steps']['curation'] = curation_result
            
            if not curation_result['success']:
                workflow_results['success'] = False
                workflow_results['errors'].append("Falha na curadoria de conteúdo")
                return workflow_results
            
            channel_id = curation_result['channel_id']
            logger.info(f"Curadoria concluída: {curation_result['relevant_videos']} vídeos relevantes")
            
            # Etapa 2: Priorização por Relevância
            logger.info("=== ETAPA 2: PRIORIZAÇÃO POR RELEVÂNCIA ===")
            priority_result = self.priority_analyzer.run_prioritization_workflow(channel_id)
            workflow_results['workflow_steps']['prioritization'] = priority_result
            
            if not priority_result['success']:
                workflow_results['success'] = False
                workflow_results['errors'].append("Falha na priorização")
                return workflow_results
            
            logger.info("Priorização concluída")
            
            # Etapa 3: Extração de Comentários
            logger.info("=== ETAPA 3: EXTRAÇÃO DE COMENTÁRIOS ===")
            extraction_result = self.comment_extractor.run_extraction_workflow(channel_id, max_videos)
            workflow_results['workflow_steps']['extraction'] = extraction_result
            
            if not extraction_result['success']:
                workflow_results['success'] = False
                workflow_results['errors'].append("Falha na extração de comentários")
                return workflow_results
            
            logger.info(f"Extração concluída: {extraction_result['total_comments_extracted']} comentários")
            
            # Etapa 4: Classificação de Comentários
            logger.info("=== ETAPA 4: CLASSIFICAÇÃO DE COMENTÁRIOS ===")
            classification_result = self.comment_classifier.run_classification_workflow(channel_id, use_ai)
            workflow_results['workflow_steps']['classification'] = classification_result
            
            if not classification_result['success']:
                workflow_results['success'] = False
                workflow_results['errors'].append("Falha na classificação")
                return workflow_results
            
            logger.info("Classificação concluída")
            
            # Etapa 5: Análise de Sentimentos
            logger.info("=== ETAPA 5: ANÁLISE DE SENTIMENTOS ===")
            sentiment_result = self.sentiment_analyzer.run_analysis_workflow(
                channel_id, selected_tags, min_confidence=0.5
            )
            workflow_results['workflow_steps']['sentiment_analysis'] = sentiment_result
            
            if not sentiment_result['success']:
                workflow_results['success'] = False
                workflow_results['errors'].append("Falha na análise de sentimentos")
                return workflow_results
            
            logger.info("Análise de sentimentos concluída")
            
            # Etapa 6: Geração de Persona
            logger.info("=== ETAPA 6: GERAÇÃO DE PERSONA ===")
            persona_result = self.persona_generator.run_persona_generation_workflow(channel_id)
            workflow_results['workflow_steps']['persona_generation'] = persona_result
            
            if not persona_result['success']:
                workflow_results['success'] = False
                workflow_results['errors'].append("Falha na geração de persona")
                return workflow_results
            
            logger.info("Geração de persona concluída")
            
            # Resumo final
            workflow_results['final_summary'] = {
                'channel_id': channel_id,
                'total_videos_analyzed': curation_result['total_videos'],
                'relevant_videos': curation_result['relevant_videos'],
                'total_comments_extracted': extraction_result['total_comments_extracted'],
                'persona_confidence': persona_result['confidence_score'],
                'persona_report_file': persona_result['report_file']
            }
            
            logger.info("=== WORKFLOW COMPLETO CONCLUÍDO COM SUCESSO ===")
            
        except Exception as e:
            logger.error(f"Erro no workflow: {e}")
            workflow_results['success'] = False
            workflow_results['errors'].append(str(e))
        
        return workflow_results
    
    def run_partial_workflow(self, step: str, channel_id: int = None, 
                           channel_url: str = None, **kwargs) -> Dict[str, Any]:
        """Executa apenas uma etapa específica do workflow"""
        logger.info(f"Executando etapa: {step}")
        
        try:
            if step == "curation" and channel_url:
                return self.content_curator.run_curation_workflow(channel_url)
            
            elif step == "prioritization" and channel_id:
                return self.priority_analyzer.run_prioritization_workflow(channel_id)
            
            elif step == "extraction" and channel_id:
                max_videos = kwargs.get('max_videos', 20)
                return self.comment_extractor.run_extraction_workflow(channel_id, max_videos)
            
            elif step == "classification" and channel_id:
                use_ai = kwargs.get('use_ai', True)
                return self.comment_classifier.run_classification_workflow(channel_id, use_ai)
            
            elif step == "sentiment_analysis" and channel_id:
                selected_tags = kwargs.get('selected_tags', ['filho', 'filha', 'familia'])
                min_confidence = kwargs.get('min_confidence', 0.5)
                return self.sentiment_analyzer.run_analysis_workflow(
                    channel_id, selected_tags, min_confidence
                )
            
            elif step == "persona_generation" and channel_id:
                return self.persona_generator.run_persona_generation_workflow(channel_id)
            
            else:
                return {
                    'success': False,
                    'error': f'Etapa "{step}" não reconhecida ou parâmetros insuficientes'
                }
                
        except Exception as e:
            logger.error(f"Erro na etapa {step}: {e}")
            return {'success': False, 'error': str(e)}
    
    def get_workflow_status(self, channel_id: int) -> Dict[str, Any]:
        """Verifica status do workflow para um canal"""
        with self.db.get_connection() as conn:
            # Verifica vídeos
            cursor = conn.execute(
                "SELECT COUNT(*) as total, SUM(is_relevant) as relevant FROM videos WHERE channel_id = ?",
                (channel_id,)
            )
            video_stats = dict(cursor.fetchone())
            
            # Verifica comentários
            cursor = conn.execute("""
                SELECT COUNT(DISTINCT c.id) as total_comments
                FROM comments c
                JOIN videos v ON c.video_id = v.id
                WHERE v.channel_id = ?
            """, (channel_id,))
            comment_stats = dict(cursor.fetchone())
            
            # Verifica tags
            cursor = conn.execute("""
                SELECT COUNT(DISTINCT ct.comment_id) as tagged_comments
                FROM comment_tags ct
                JOIN comments c ON ct.comment_id = c.id
                JOIN videos v ON c.video_id = v.id
                WHERE v.channel_id = ?
            """, (channel_id,))
            tag_stats = dict(cursor.fetchone())
            
            # Verifica análises
            cursor = conn.execute("""
                SELECT COUNT(DISTINCT ca.comment_id) as analyzed_comments
                FROM comment_analysis ca
                JOIN comments c ON ca.comment_id = c.id
                JOIN videos v ON c.video_id = v.id
                WHERE v.channel_id = ?
            """, (channel_id,))
            analysis_stats = dict(cursor.fetchone())
            
            # Verifica personas
            cursor = conn.execute(
                "SELECT COUNT(*) as personas FROM personas WHERE channel_id = ?",
                (channel_id,)
            )
            persona_stats = dict(cursor.fetchone())
        
        return {
            'channel_id': channel_id,
            'videos': video_stats,
            'comments': comment_stats,
            'tags': tag_stats,
            'analyses': analysis_stats,
            'personas': persona_stats,
            'workflow_complete': all([
                video_stats['relevant'] > 0,
                comment_stats['total_comments'] > 0,
                tag_stats['tagged_comments'] > 0,
                analysis_stats['analyzed_comments'] > 0,
                persona_stats['personas'] > 0
            ])
        }


def main():
    """Função principal do script"""
    parser = argparse.ArgumentParser(
        description="PersonaCreator - Análise de personas baseada em comentários do YouTube"
    )
    
    parser.add_argument(
        '--channel-url', 
        required=True,
        help='URL do canal do YouTube para análise'
    )
    
    parser.add_argument(
        '--max-videos',
        type=int,
        default=20,
        help='Número máximo de vídeos para analisar (padrão: 20)'
    )
    
    parser.add_argument(
        '--tags',
        nargs='+',
        default=['filho', 'filha', 'familia', 'educacao'],
        help='Tags para filtrar comentários (padrão: filho filha familia educacao)'
    )
    
    parser.add_argument(
        '--step',
        choices=['curation', 'prioritization', 'extraction', 'classification', 'sentiment_analysis', 'persona_generation'],
        help='Executar apenas uma etapa específica'
    )
    
    parser.add_argument(
        '--channel-id',
        type=int,
        help='ID do canal no banco (necessário para etapas específicas)'
    )
    
    parser.add_argument(
        '--no-ai',
        action='store_true',
        help='Desabilita uso de IA (usa métodos simples)'
    )
    
    parser.add_argument(
        '--status',
        action='store_true',
        help='Mostra status do workflow para um canal'
    )
    
    args = parser.parse_args()
    
    # Inicializa orquestrador
    orchestrator = PersonaCreatorOrchestrator()
    
    try:
        if args.status and args.channel_id:
            # Mostra status
            status = orchestrator.get_workflow_status(args.channel_id)
            print(f"\n=== STATUS DO WORKFLOW - Canal {args.channel_id} ===")
            print(f"Vídeos: {status['videos']['total']} total, {status['videos']['relevant']} relevantes")
            print(f"Comentários: {status['comments']['total_comments']}")
            print(f"Comentários com tags: {status['tags']['tagged_comments']}")
            print(f"Comentários analisados: {status['analyses']['analyzed_comments']}")
            print(f"Personas geradas: {status['personas']['personas']}")
            print(f"Workflow completo: {'✓' if status['workflow_complete'] else '✗'}")
            
        elif args.step:
            # Executa etapa específica
            result = orchestrator.run_partial_workflow(
                step=args.step,
                channel_id=args.channel_id,
                channel_url=args.channel_url,
                max_videos=args.max_videos,
                selected_tags=args.tags,
                use_ai=not args.no_ai
            )
            
            if result['success']:
                print(f"\n✓ Etapa '{args.step}' concluída com sucesso!")
                logger.info(f"Resultado: {result}")
            else:
                print(f"\n✗ Erro na etapa '{args.step}': {result.get('error', 'Erro desconhecido')}")
                sys.exit(1)
        
        else:
            # Executa workflow completo
            result = orchestrator.run_full_workflow(
                channel_url=args.channel_url,
                max_videos=args.max_videos,
                selected_tags=args.tags,
                use_ai=not args.no_ai
            )
            
            if result['success']:
                print("\n🎉 WORKFLOW COMPLETO CONCLUÍDO COM SUCESSO!")
                print(f"📊 Resumo:")
                summary = result['final_summary']
                print(f"   • Canal ID: {summary['channel_id']}")
                print(f"   • Vídeos analisados: {summary['total_videos_analyzed']}")
                print(f"   • Vídeos relevantes: {summary['relevant_videos']}")
                print(f"   • Comentários extraídos: {summary['total_comments_extracted']}")
                print(f"   • Confiança da persona: {summary['persona_confidence']:.2%}")
                print(f"   • Relatório: {summary['persona_report_file']}")
            else:
                print(f"\n❌ WORKFLOW FALHOU!")
                print(f"Erros: {', '.join(result['errors'])}")
                sys.exit(1)
    
    except KeyboardInterrupt:
        print("\n\n⚠️  Workflow interrompido pelo usuário")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Erro fatal: {e}")
        print(f"\n💥 Erro fatal: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
