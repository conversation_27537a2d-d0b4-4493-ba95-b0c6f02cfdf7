# PersonaCreator 🎯

Sistema inteligente de análise de personas baseado em comentários do YouTube, utilizando IA para extrair insights sobre dores, desejos, sonhos e oportunidades de negócio do público-alvo.

## 🚀 Funcionalidades

- **Curadoria Inteligente**: Analisa vídeos de canais do YouTube e identifica conteúdo relevante sobre parentalidade
- **Priorização por IA**: Ordena vídeos por potencial de análise usando métricas de engajamento e relevância
- **Extração de Comentários**: Coleta e filtra comentários relevantes dos vídeos priorizados
- **Classificação Automática**: Aplica tags contextuais aos comentários usando IA
- **Análise de Sentimentos**: Extrai dores, desejos, sonhos, frustrações e oportunidades
- **Geração de Persona**: Cria persona detalhada baseada em todos os insights coletados

## 🛠️ Tecnologias Utilizadas

- **Python 3.8+**
- **yt-dlp**: Extração de dados do YouTube
- **CrewAI**: Framework de agentes de IA
- **SQLite**: Banco de dados local
- **LLaMA 3 8B**: Modelo de linguagem (via Ollama)
- **Loguru**: Sistema de logging avançado

## 📋 Pré-requisitos

### 1. Instalar Python 3.8+
```bash
python --version  # Deve ser 3.8 ou superior
```

### 2. Instalar Ollama e LLaMA 3
```bash
# Instalar Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# Baixar modelo LLaMA 3 8B
ollama pull llama3:8b-instruct
```

### 3. Clonar e configurar o projeto
```bash
git clone <seu-repositorio>
cd personacreator

# Instalar dependências
pip install -r requirements.txt

# Configurar variáveis de ambiente (opcional)
cp .env.example .env
# Edite o arquivo .env com suas configurações
```

## 🚀 Como Usar

### Uso Básico - Workflow Completo
```bash
python main.py --channel-url "https://www.youtube.com/@canalexemplo"
```

### Opções Avançadas
```bash
# Limitar número de vídeos analisados
python main.py --channel-url "https://www.youtube.com/@canal" --max-videos 10

# Especificar tags para filtrar comentários
python main.py --channel-url "https://www.youtube.com/@canal" --tags filho filha educacao saude

# Desabilitar IA (usar métodos simples)
python main.py --channel-url "https://www.youtube.com/@canal" --no-ai
```

### Executar Etapas Específicas
```bash
# Apenas curadoria de conteúdo
python main.py --step curation --channel-url "https://www.youtube.com/@canal"

# Apenas análise de sentimentos (precisa do channel-id)
python main.py --step sentiment_analysis --channel-id 1 --tags filho filha

# Ver status do workflow
python main.py --status --channel-id 1
```

## 📊 Estrutura do Workflow

### 1. **Curadoria de Conteúdo** 🎬
- Extrai lista de vídeos do canal
- Analisa títulos para relevância com parentalidade
- Salva vídeos relevantes no banco de dados

### 2. **Priorização por Relevância** 📈
- Calcula scores de engajamento
- Analisa potencial de discussão dos títulos
- Ordena vídeos por prioridade de análise

### 3. **Extração de Comentários** 💬
- Baixa comentários dos vídeos priorizados
- Filtra comentários relevantes
- Remove spam e conteúdo irrelevante

### 4. **Classificação de Comentários** 🏷️
- Aplica tags contextuais usando IA
- Categoriza comentários por temas
- Calcula scores de confiança

### 5. **Análise de Sentimentos** 🧠
- Extrai dores e frustrações
- Identifica desejos e sonhos
- Encontra palavras mágicas e oportunidades

### 6. **Geração de Persona** 👤
- Consolida todos os insights
- Cria persona detalhada
- Gera relatório em JSON e Markdown

## 📁 Estrutura de Arquivos

```
personacreator/
├── main.py                 # Script principal
├── config.py              # Configurações do sistema
├── database.py            # Gerenciamento do banco SQLite
├── ai_utils.py            # Utilitários de IA
├── requirements.txt       # Dependências Python
├── agents/               # Módulos dos agentes
│   ├── content_curator.py
│   ├── priority_analyzer.py
│   ├── comment_extractor.py
│   ├── comment_classifier.py
│   ├── sentiment_analyzer.py
│   └── persona_generator.py
├── data/                 # Banco de dados
├── logs/                 # Arquivos de log
└── output/              # Relatórios gerados
```

## 📈 Exemplo de Saída

### Persona Gerada
```json
{
  "nome_persona": "Maria Educadora",
  "demografia": {
    "idade": "32-38 anos",
    "localização": "Região Sudeste, Brasil",
    "situação_familiar": "casal",
    "filhos": "3-5 anos",
    "renda": "Classe média (R$ 3.000-8.000)"
  },
  "dores_principais": [
    "Dificuldade para estabelecer limites",
    "Falta de tempo para os filhos",
    "Preocupação com desenvolvimento escolar"
  ],
  "desejos_principais": [
    "Filhos bem-educados e respeitosos",
    "Equilibrio entre trabalho e família",
    "Métodos educativos eficazes"
  ],
  "palavras_magicas": [
    "amor incondicional", "desenvolvimento saudável", 
    "educação positiva", "família unida"
  ],
  "oportunidades_negocio": [
    "Consultoria em educação parental",
    "Cursos online para pais",
    "Produtos organizacionais familiares"
  ]
}
```

## 🔧 Configurações Avançadas

### Arquivo .env
```env
# API do YouTube (opcional - para mais dados)
YOUTUBE_API_KEY=sua_chave_aqui

# Configurações do LLaMA
LLAMA_API_URL=http://localhost:11434

# Limites de análise
MAX_VIDEOS_PER_CHANNEL=100
MAX_COMMENTS_PER_VIDEO=500

# Nível de logging
LOG_LEVEL=INFO
```

### Personalizar Tags de Comentários
Edite o arquivo `config.py` para personalizar as tags:

```python
COMMENT_TAGS = {
    "filho": ["meu filho", "filho", "garoto", "menino"],
    "filha": ["minha filha", "filha", "garota", "menina"],
    "educacao": ["escola", "estudo", "aprender", "ensinar"],
    # Adicione suas próprias tags...
}
```

## 🐛 Solução de Problemas

### Erro: "Modelo LLaMA não encontrado"
```bash
# Verificar se Ollama está rodando
ollama list

# Baixar modelo se necessário
ollama pull llama3:8b-instruct
```

### Erro: "Nenhum vídeo relevante encontrado"
- Verifique se o canal tem conteúdo sobre parentalidade
- Ajuste as palavras-chave em `config.py`
- Use `--no-ai` para análise mais permissiva

### Performance lenta
- Reduza `--max-videos` para análises menores
- Use `--no-ai` para processamento mais rápido
- Verifique se o Ollama está configurado corretamente

## 📝 Logs e Monitoramento

Os logs são salvos em:
- **Console**: Nível INFO
- **Arquivo**: `logs/persona_creator.log` (nível DEBUG)

Para monitorar em tempo real:
```bash
tail -f logs/persona_creator.log
```

## 🤝 Contribuindo

1. Fork o projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## 📄 Licença

Este projeto está sob a licença MIT. Veja o arquivo `LICENSE` para detalhes.

## 🆘 Suporte

Para dúvidas e suporte:
- Abra uma issue no GitHub
- Consulte os logs em `logs/persona_creator.log`
- Verifique a documentação dos agentes em `agents/`

---

**PersonaCreator** - Transformando comentários em insights acionáveis! 🚀
