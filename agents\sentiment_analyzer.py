"""
Agente 5 - <PERSON><PERSON><PERSON> de Dores e Desejos
Responsável por extrair dores, sonhos, desejos, frustrações e oportunidades dos comentários
"""
import json
import re
from datetime import datetime
from typing import List, Dict, Any, Optional
from pathlib import Path
import sys
sys.path.append(str(Path(__file__).parent.parent))

from loguru import logger
from crewai import Agent, Task

from database import DatabaseManager
from ai_utils import LLaMAClient, AnalysisPrompts
from config import ANALYSIS_CATEGORIES, OUTPUT_DIR


class SentimentAnalyzerAgent:
    """Agente analisador de sentimentos e extração de insights"""
    
    def __init__(self, db_manager: DatabaseManager, llama_client: LLaMAClient):
        self.db = db_manager
        self.llama = llama_client
        self.analysis_categories = ANALYSIS_CATEGORIES
    
    def extract_pain_points(self, comment_text: str) -> Dict[str, Any]:
        """Extrai dores e frustrações de um comentário"""
        prompt = AnalysisPrompts.extract_pain_points(comment_text)
        response = self.llama.generate_response(prompt, temperature=0.3)
        
        try:
            result = json.loads(response)
            return {
                'type': 'dores',
                'content': result.get('dores_identificadas', []),
                'intensity': result.get('nivel_intensidade', 'baixo'),
                'category': result.get('categoria_principal', ''),
                'confidence': result.get('confianca', 0.0)
            }
        except Exception as e:
            logger.warning(f"Erro ao extrair dores: {e}")
            return self._extract_pain_points_simple(comment_text)
    
    def _extract_pain_points_simple(self, comment_text: str) -> Dict[str, Any]:
        """Extração simples de dores baseada em palavras-chave"""
        pain_keywords = self.analysis_categories.get('dores', [])
        frustration_keywords = self.analysis_categories.get('frustracoes', [])
        
        text_lower = comment_text.lower()
        found_pains = []
        
        # Busca por padrões de dor/problema
        pain_patterns = [
            r'não consigo',
            r'tenho dificuldade',
            r'é muito difícil',
            r'me preocupa',
            r'tenho medo',
            r'não sei como',
            r'estou cansad[ao]',
            r'me frustra'
        ]
        
        for pattern in pain_patterns:
            matches = re.findall(pattern, text_lower)
            if matches:
                # Extrai contexto ao redor da expressão
                for match in matches:
                    start = text_lower.find(match)
                    context = comment_text[max(0, start-20):start+len(match)+30]
                    found_pains.append(context.strip())
        
        # Busca por palavras-chave de dor
        for keyword in pain_keywords + frustration_keywords:
            if keyword.lower() in text_lower:
                # Extrai frase que contém a palavra-chave
                sentences = comment_text.split('.')
                for sentence in sentences:
                    if keyword.lower() in sentence.lower():
                        found_pains.append(sentence.strip())
                        break
        
        confidence = min(len(found_pains) * 0.3, 1.0)
        
        return {
            'type': 'dores',
            'content': found_pains[:5],  # Limita a 5 dores
            'intensity': 'médio' if confidence > 0.5 else 'baixo',
            'category': 'geral',
            'confidence': confidence
        }
    
    def extract_desires_dreams(self, comment_text: str) -> Dict[str, Any]:
        """Extrai desejos e sonhos de um comentário"""
        prompt = AnalysisPrompts.extract_desires_dreams(comment_text)
        response = self.llama.generate_response(prompt, temperature=0.3)
        
        try:
            result = json.loads(response)
            return {
                'type': 'desejos_sonhos',
                'desires': result.get('desejos', []),
                'dreams': result.get('sonhos', []),
                'objectives': result.get('objetivos', []),
                'category': result.get('categoria_principal', ''),
                'confidence': result.get('confianca', 0.0)
            }
        except Exception as e:
            logger.warning(f"Erro ao extrair desejos: {e}")
            return self._extract_desires_simple(comment_text)
    
    def _extract_desires_simple(self, comment_text: str) -> Dict[str, Any]:
        """Extração simples de desejos baseada em palavras-chave"""
        desire_keywords = self.analysis_categories.get('desejos', [])
        dream_keywords = self.analysis_categories.get('sonhos', [])
        
        text_lower = comment_text.lower()
        found_desires = []
        found_dreams = []
        
        # Padrões de desejo
        desire_patterns = [
            r'quero que',
            r'gostaria que',
            r'espero que',
            r'desejo que',
            r'sonho em',
            r'meu objetivo é',
            r'pretendo'
        ]
        
        for pattern in desire_patterns:
            matches = re.findall(pattern, text_lower)
            if matches:
                for match in matches:
                    start = text_lower.find(match)
                    context = comment_text[start:start+100]
                    found_desires.append(context.strip())
        
        # Busca por palavras-chave
        for keyword in desire_keywords:
            if keyword.lower() in text_lower:
                sentences = comment_text.split('.')
                for sentence in sentences:
                    if keyword.lower() in sentence.lower():
                        found_desires.append(sentence.strip())
                        break
        
        for keyword in dream_keywords:
            if keyword.lower() in text_lower:
                sentences = comment_text.split('.')
                for sentence in sentences:
                    if keyword.lower() in sentence.lower():
                        found_dreams.append(sentence.strip())
                        break
        
        confidence = min((len(found_desires) + len(found_dreams)) * 0.3, 1.0)
        
        return {
            'type': 'desejos_sonhos',
            'desires': found_desires[:3],
            'dreams': found_dreams[:3],
            'objectives': [],
            'category': 'geral',
            'confidence': confidence
        }
    
    def extract_magic_words_opportunities(self, comment_text: str) -> Dict[str, Any]:
        """Extrai palavras mágicas e oportunidades de negócio"""
        prompt = AnalysisPrompts.extract_magic_words(comment_text)
        response = self.llama.generate_response(prompt, temperature=0.3)
        
        try:
            result = json.loads(response)
            return {
                'type': 'palavras_magicas_oportunidades',
                'magic_words': result.get('palavras_magicas', []),
                'business_opportunities': result.get('oportunidades_negocio', []),
                'effective_language': result.get('linguagem_efetiva', []),
                'confidence': result.get('confianca', 0.0)
            }
        except Exception as e:
            logger.warning(f"Erro ao extrair palavras mágicas: {e}")
            return self._extract_magic_words_simple(comment_text)
    
    def _extract_magic_words_simple(self, comment_text: str) -> Dict[str, Any]:
        """Extração simples de palavras mágicas"""
        magic_keywords = self.analysis_categories.get('palavras_magicas', [])
        opportunity_keywords = self.analysis_categories.get('oportunidades', [])
        
        text_lower = comment_text.lower()
        found_magic = []
        found_opportunities = []
        
        # Busca palavras mágicas
        for keyword in magic_keywords:
            if keyword.lower() in text_lower:
                found_magic.append(keyword)
        
        # Busca oportunidades
        opportunity_patterns = [
            r'preciso de',
            r'gostaria de ter',
            r'seria útil',
            r'falta no mercado',
            r'ninguém oferece',
            r'deveria existir'
        ]
        
        for pattern in opportunity_patterns:
            if re.search(pattern, text_lower):
                start = text_lower.find(pattern)
                context = comment_text[start:start+80]
                found_opportunities.append(context.strip())
        
        confidence = min((len(found_magic) + len(found_opportunities)) * 0.2, 1.0)
        
        return {
            'type': 'palavras_magicas_oportunidades',
            'magic_words': found_magic,
            'business_opportunities': found_opportunities,
            'effective_language': [],
            'confidence': confidence
        }
    
    def analyze_single_comment(self, comment: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Analisa um único comentário extraindo todos os insights"""
        comment_text = comment.get('text', '')
        if not comment_text or len(comment_text) < 20:
            return []
        
        analyses = []
        
        # Extrai dores
        pain_analysis = self.extract_pain_points(comment_text)
        if pain_analysis['confidence'] > 0.3:
            analyses.append(pain_analysis)
        
        # Extrai desejos e sonhos
        desire_analysis = self.extract_desires_dreams(comment_text)
        if desire_analysis['confidence'] > 0.3:
            analyses.append(desire_analysis)
        
        # Extrai palavras mágicas e oportunidades
        magic_analysis = self.extract_magic_words_opportunities(comment_text)
        if magic_analysis['confidence'] > 0.3:
            analyses.append(magic_analysis)
        
        return analyses
    
    def save_comment_analysis(self, comment_id: int, analyses: List[Dict[str, Any]]):
        """Salva análises de um comentário no banco de dados"""
        for analysis in analyses:
            analysis_type = analysis.get('type', '')
            confidence = analysis.get('confidence', 0.0)
            
            if analysis_type == 'dores':
                content = '; '.join(analysis.get('content', []))
                keywords = analysis.get('content', [])
            elif analysis_type == 'desejos_sonhos':
                desires = analysis.get('desires', [])
                dreams = analysis.get('dreams', [])
                objectives = analysis.get('objectives', [])
                content = f"Desejos: {'; '.join(desires)}. Sonhos: {'; '.join(dreams)}. Objetivos: {'; '.join(objectives)}"
                keywords = desires + dreams + objectives
            elif analysis_type == 'palavras_magicas_oportunidades':
                magic = analysis.get('magic_words', [])
                opportunities = analysis.get('business_opportunities', [])
                content = f"Palavras mágicas: {'; '.join(magic)}. Oportunidades: {'; '.join(opportunities)}"
                keywords = magic + opportunities
            else:
                continue
            
            if content and confidence > 0:
                self.db.insert_comment_analysis(
                    comment_id=comment_id,
                    analysis_type=analysis_type,
                    extracted_content=content,
                    confidence_score=confidence,
                    keywords=keywords[:10],  # Limita a 10 keywords
                    sentiment_score=0.0  # Pode ser implementado posteriormente
                )
    
    def analyze_tagged_comments(self, channel_id: int, selected_tags: List[str], 
                              min_confidence: float = 0.5, batch_size: int = 50) -> Dict[str, Any]:
        """Analisa comentários que possuem tags específicas"""
        logger.info(f"Analisando comentários com tags: {', '.join(selected_tags)}")
        
        # Busca comentários com as tags selecionadas que ainda não foram analisados
        with self.db.get_connection() as conn:
            placeholders = ','.join(['?' for _ in selected_tags])
            
            cursor = conn.execute(f"""
                SELECT DISTINCT c.id, c.text, c.author, c.like_count
                FROM comments c
                JOIN videos v ON c.video_id = v.id
                JOIN comment_tags ct ON c.id = ct.comment_id
                LEFT JOIN comment_analysis ca ON c.id = ca.comment_id
                WHERE v.channel_id = ? 
                AND ct.tag_name IN ({placeholders})
                AND ct.confidence_score >= ?
                AND ca.id IS NULL
                ORDER BY c.like_count DESC, LENGTH(c.text) DESC
            """, [channel_id] + selected_tags + [min_confidence])
            
            unanalyzed_comments = [dict(row) for row in cursor.fetchall()]
        
        if not unanalyzed_comments:
            logger.info("Nenhum comentário não analisado encontrado")
            return {'success': True, 'message': 'Todos os comentários já foram analisados'}
        
        logger.info(f"Encontrados {len(unanalyzed_comments)} comentários para analisar")
        
        analysis_stats = {
            'total_processed': 0,
            'total_analyses': 0,
            'analysis_distribution': {},
            'comments_with_insights': 0
        }
        
        # Processa comentários em lotes
        for i in range(0, len(unanalyzed_comments), batch_size):
            batch = unanalyzed_comments[i:i + batch_size]
            logger.info(f"Processando lote {i//batch_size + 1}/{(len(unanalyzed_comments) + batch_size - 1)//batch_size}")
            
            for comment in batch:
                try:
                    # Analisa comentário
                    analyses = self.analyze_single_comment(comment)
                    
                    if analyses:
                        # Salva análises no banco
                        self.save_comment_analysis(comment['id'], analyses)
                        
                        # Atualiza estatísticas
                        analysis_stats['comments_with_insights'] += 1
                        analysis_stats['total_analyses'] += len(analyses)
                        
                        for analysis in analyses:
                            analysis_type = analysis.get('type', '')
                            if analysis_type:
                                analysis_stats['analysis_distribution'][analysis_type] = \
                                    analysis_stats['analysis_distribution'].get(analysis_type, 0) + 1
                    
                    analysis_stats['total_processed'] += 1
                    
                except Exception as e:
                    logger.warning(f"Erro ao analisar comentário {comment['id']}: {e}")
        
        result = {
            'success': True,
            'channel_id': channel_id,
            'selected_tags': selected_tags,
            'analysis_stats': analysis_stats,
            'insight_rate': analysis_stats['comments_with_insights'] / analysis_stats['total_processed'] if analysis_stats['total_processed'] > 0 else 0
        }
        
        logger.info(f"Análise concluída: {analysis_stats['comments_with_insights']}/{analysis_stats['total_processed']} comentários com insights")
        return result

    def export_analysis_summary(self, channel_id: int, output_file: str = None) -> str:
        """Exporta resumo das análises realizadas"""
        if not output_file:
            output_file = OUTPUT_DIR / "analysis_summary.json"

        # Busca resumo das análises
        analysis_summary = self.db.get_analysis_summary(channel_id)

        # Estatísticas por tipo de análise
        with self.db.get_connection() as conn:
            cursor = conn.execute("""
                SELECT
                    ca.analysis_type,
                    COUNT(*) as count,
                    AVG(ca.confidence_score) as avg_confidence,
                    MAX(ca.confidence_score) as max_confidence
                FROM comment_analysis ca
                JOIN comments c ON ca.comment_id = c.id
                JOIN videos v ON c.video_id = v.id
                WHERE v.channel_id = ?
                GROUP BY ca.analysis_type
                ORDER BY count DESC
            """, (channel_id,))

            type_stats = [dict(row) for row in cursor.fetchall()]

        # Top insights por categoria
        top_insights = {}
        for analysis_type in ['dores', 'desejos_sonhos', 'palavras_magicas_oportunidades']:
            with self.db.get_connection() as conn:
                cursor = conn.execute("""
                    SELECT ca.extracted_content, ca.confidence_score, c.like_count
                    FROM comment_analysis ca
                    JOIN comments c ON ca.comment_id = c.id
                    JOIN videos v ON c.video_id = v.id
                    WHERE v.channel_id = ? AND ca.analysis_type = ?
                    ORDER BY ca.confidence_score DESC, c.like_count DESC
                    LIMIT 10
                """, (channel_id, analysis_type))

                top_insights[analysis_type] = [dict(row) for row in cursor.fetchall()]

        export_data = {
            'channel_id': channel_id,
            'analysis_date': str(datetime.now()),
            'analysis_summary': analysis_summary,
            'type_statistics': type_stats,
            'top_insights_by_category': top_insights,
            'total_analyses': sum(stat['count'] for stat in type_stats)
        }

        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)

        logger.info(f"Resumo de análises exportado para: {output_file}")
        return str(output_file)

    def run_analysis_workflow(self, channel_id: int, selected_tags: List[str],
                            min_confidence: float = 0.5) -> Dict[str, Any]:
        """Executa workflow completo de análise de sentimentos"""
        logger.info("Iniciando workflow de análise de sentimentos")

        # 1. Analisa comentários com tags selecionadas
        result = self.analyze_tagged_comments(channel_id, selected_tags, min_confidence)

        if not result['success']:
            return result

        # 2. Exporta resumo das análises
        export_file = self.export_analysis_summary(channel_id)
        result['export_file'] = export_file

        logger.info("Workflow de análise de sentimentos concluído")
        return result


def create_sentiment_analyzer_crew_agent() -> Agent:
    """Cria agente CrewAI para análise de sentimentos"""
    return Agent(
        role='Analisador de Sentimentos e Insights',
        goal='Extrair dores, desejos, sonhos, frustrações e oportunidades dos comentários classificados',
        backstory="""Você é um psicólogo especialista em parentalidade e análise de comportamento
        consumer. Sua expertise está em identificar as necessidades emocionais profundas, medos,
        aspirações e oportunidades não atendidas nos comentários de pais e mães, transformando
        texto em insights acionáveis para criação de personas.""",
        verbose=True,
        allow_delegation=False
    )


def create_sentiment_analysis_task(agent: Agent, channel_id: int, selected_tags: List[str]) -> Task:
    """Cria task CrewAI para análise de sentimentos"""
    tags_str = ', '.join(selected_tags)

    return Task(
        description=f"""
        Analise os comentários classificados com as tags: {tags_str} do canal ID: {channel_id}

        Suas responsabilidades:
        1. Extrair dores e frustrações relacionadas à parentalidade
        2. Identificar desejos, sonhos e aspirações para os filhos
        3. Encontrar palavras e expressões que geram engajamento positivo
        4. Identificar oportunidades de negócio e necessidades não atendidas
        5. Calcular scores de confiança para cada insight extraído

        Foque em:
        - Dores: medos, preocupações, dificuldades, estresse parental
        - Desejos: aspirações para os filhos, objetivos educacionais
        - Sonhos: visões de futuro, ideais parentais
        - Frustrações: decepções, falhas percebidas, arrependimentos
        - Palavras mágicas: termos que geram conexão emocional
        - Oportunidades: lacunas no mercado, necessidades não atendidas

        Seja específico e baseie-se no contexto real dos comentários.
        """,
        agent=agent,
        expected_output="Base de insights estruturados por categoria com scores de confiança"
    )
