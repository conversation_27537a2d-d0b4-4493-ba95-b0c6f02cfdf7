# YouTube data extraction
yt-dlp==2023.12.30

# AI and ML frameworks
crewai==0.28.8
langchain==0.1.20
langchain-community==0.0.38
transformers==4.36.2
torch==2.1.2

# Database
sqlite3

# Data processing
pandas==2.1.4
numpy==1.24.4

# HTTP requests and APIs
requests==2.31.0
aiohttp==3.9.1

# Configuration and environment
python-dotenv==1.0.0
pydantic==2.5.2

# Logging and utilities
loguru==0.7.2
tqdm==4.66.1

# JSON and data serialization
orjson==3.9.10

# Optional: For local LLaMA model (if not using API)
# llama-cpp-python==0.2.27
# huggingface-hub==0.19.4
