"""
Configurações do projeto PersonaCreator
"""
import os
from pathlib import Path
from dotenv import load_dotenv

# Carrega variáveis de ambiente
load_dotenv()

# Diretórios do projeto
PROJECT_ROOT = Path(__file__).parent
DATA_DIR = PROJECT_ROOT / "data"
LOGS_DIR = PROJECT_ROOT / "logs"
OUTPUT_DIR = PROJECT_ROOT / "output"

# Cria diretórios se não existirem
for directory in [DATA_DIR, LOGS_DIR, OUTPUT_DIR]:
    directory.mkdir(exist_ok=True)

# Configurações do banco de dados
DATABASE_PATH = DATA_DIR / "persona_creator.db"

# Configurações da IA
LLAMA_MODEL_NAME = "llama3-8b-instruct"
LLAMA_API_URL = os.getenv("LLAMA_API_URL", "http://localhost:11434")  # Ollama default
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")  # Fallback para OpenAI se necessário

# Configurações do YouTube
YOUTUBE_API_KEY = os.getenv("YOUTUBE_API_KEY")
MAX_VIDEOS_PER_CHANNEL = int(os.getenv("MAX_VIDEOS_PER_CHANNEL", "100"))
MAX_COMMENTS_PER_VIDEO = int(os.getenv("MAX_COMMENTS_PER_VIDEO", "500"))

# Configurações de análise
TARGET_CONTEXT_KEYWORDS = [
    "pais", "filhos", "família", "criança", "educação", "parentalidade",
    "mãe", "pai", "bebê", "adolescente", "desenvolvimento"
]

COMMENT_TAGS = {
    "filho": ["meu filho", "filho", "filhinho", "garoto", "menino"],
    "filha": ["minha filha", "filha", "filhinha", "garota", "menina"],
    "familia": ["família", "casa", "lar", "parentes"],
    "educacao": ["escola", "estudo", "aprender", "ensinar", "educação"],
    "comportamento": ["comportamento", "disciplina", "birra", "obediência"],
    "saude": ["saúde", "médico", "doença", "cuidado", "bem-estar"],
    "desenvolvimento": ["crescimento", "desenvolvimento", "idade", "fase"]
}

# Configurações de logging
LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
LOG_FORMAT = "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"

# Configurações de análise de sentimentos e extração
ANALYSIS_CATEGORIES = {
    "dores": [
        "problema", "dificuldade", "preocupação", "medo", "ansiedade",
        "estresse", "cansaço", "frustração", "raiva", "tristeza"
    ],
    "desejos": [
        "quero", "desejo", "sonho", "espero", "gostaria", "almejo",
        "pretendo", "planejo", "objetivo", "meta"
    ],
    "sonhos": [
        "sonho", "futuro", "esperança", "ambição", "ideal", "visão",
        "aspiração", "projeto de vida"
    ],
    "frustracoes": [
        "frustração", "decepção", "falha", "erro", "arrependimento",
        "insatisfação", "descontentamento"
    ],
    "palavras_magicas": [
        "incrível", "fantástico", "maravilhoso", "perfeito", "excelente",
        "ótimo", "adorei", "amei", "recomendo", "vale a pena"
    ],
    "oportunidades": [
        "oportunidade", "chance", "possibilidade", "negócio", "investimento",
        "mercado", "demanda", "necessidade", "solução"
    ]
}
