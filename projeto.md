1 parte:

1.1 - Listar todos os vídeos de um canal do YouTube e salvar no banco;
1.2 - IA analisar o título de cada vídeo buscando quais vídeos falam do contexto que eu estou querendo e salvar a lista em um txt juntamente com o contexto identificado no título do vídeo;
1.3 - IA analisa o contexto de cada vídeo e coloca por ordem de importância;

2 parte:

2.1 - O script extrai e salva os comentários de cada vídeo iniciando com os de importância maior;
2.2 - O script coloca tags nos comentários que tem determinadas palavras que desejamos ex: meu filho, minha filha, etc…
2.3 - Selecionamos uma ou mais tags e a IA analisa cada comentário que contém essas tags buscando extrair as dores, desejos, sonhos, frustrações, palavras mágicas e oportunidades de negocio;

3 parte:

3.1 - A IA analisa as as dores, desejos, sonhos, frustrações e palavras mágicas extraídas de todos os vídeos e constrói a persona;

o que vamos utilizar:

yt-dlp
CrewAI
SQLite
LLaMA 3 8B 



Estrutura do Projeto (Workflow Autônomo)
Input: URL de canal do YouTube
Agente 1 – Curador de Conteúdo

Lista os vídeos do canal (com yt-dlp)

Analisa os títulos e decide quais têm relação com o contexto-alvo (ex: “pais e filhos”)

Salva isso em um .txt ou .json

Agente 2 – Prioriza por Relevância

Analisa os títulos selecionados e ordena por relevância (com base em palavras-chave, views, etc)

Agente 3 – Extrator de Comentários

Baixa os comentários dos vídeos mais relevantes

Armazena comentários em banco ou arquivos

Agente 4 – Classificador de Comentários

Aplica tags (ex: “meu filho”, “minha filha”)

Separa comentários por tag

Agente 5 – Analisador de Dores e Desejos

Extrai dores, sonhos, desejos, frustrações, palavras mágicas, e oportunidades

Agente 6 – Gerador de Persona

Com base na análise, gera uma persona detalhada do público do canal.