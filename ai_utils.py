"""
Utilitários para integração com modelos de IA (LLaMA 3 via Ollama)
"""
import requests
import json
from typing import Dict, List, Any, Optional
from loguru import logger
from config import LLAMA_API_URL, LLAMA_MODEL_NAME


class LLaMAClient:
    """Cliente para comunicação com LLaMA 3 via Ollama"""
    
    def __init__(self, api_url: str = LLAMA_API_URL, model_name: str = LLAMA_MODEL_NAME):
        self.api_url = api_url.rstrip('/')
        self.model_name = model_name
        self.session = requests.Session()
    
    def generate_response(self, prompt: str, system_prompt: str = None, 
                         temperature: float = 0.7, max_tokens: int = 1000) -> str:
        """Gera resposta usando LLaMA 3"""
        try:
            payload = {
                "model": self.model_name,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": temperature,
                    "num_predict": max_tokens
                }
            }
            
            if system_prompt:
                payload["system"] = system_prompt
            
            response = self.session.post(
                f"{self.api_url}/api/generate",
                json=payload,
                timeout=60
            )
            response.raise_for_status()
            
            result = response.json()
            return result.get("response", "").strip()
            
        except Exception as e:
            logger.error(f"Erro ao gerar resposta com LLaMA: {e}")
            return ""
    
    def analyze_text_sentiment(self, text: str) -> Dict[str, Any]:
        """Analisa sentimento de um texto"""
        system_prompt = """Você é um especialista em análise de sentimentos. 
        Analise o texto fornecido e retorne um JSON com:
        - sentiment: "positivo", "negativo" ou "neutro"
        - confidence: score de 0 a 1
        - emotions: lista de emoções identificadas
        - intensity: "baixa", "média" ou "alta"
        """
        
        prompt = f"Analise o sentimento do seguinte texto:\n\n{text}"
        
        response = self.generate_response(prompt, system_prompt, temperature=0.3)
        
        try:
            return json.loads(response)
        except:
            return {
                "sentiment": "neutro",
                "confidence": 0.0,
                "emotions": [],
                "intensity": "baixa"
            }
    
    def extract_keywords(self, text: str, max_keywords: int = 10) -> List[str]:
        """Extrai palavras-chave de um texto"""
        system_prompt = f"""Você é um especialista em extração de palavras-chave.
        Extraia as {max_keywords} palavras-chave mais importantes do texto fornecido.
        Retorne apenas uma lista JSON de strings, sem explicações.
        """
        
        prompt = f"Extraia palavras-chave do seguinte texto:\n\n{text}"
        
        response = self.generate_response(prompt, system_prompt, temperature=0.3)
        
        try:
            keywords = json.loads(response)
            return keywords if isinstance(keywords, list) else []
        except:
            return []


class AnalysisPrompts:
    """Prompts especializados para diferentes tipos de análise"""
    
    @staticmethod
    def video_title_relevance(title: str, context_keywords: List[str]) -> str:
        """Prompt para analisar relevância de título de vídeo"""
        keywords_str = ", ".join(context_keywords)
        return f"""
        Você é um especialista em análise de conteúdo sobre parentalidade e família.
        
        Analise se o título do vídeo abaixo está relacionado ao contexto de pais, filhos e família.
        
        Título: "{title}"
        Palavras-chave do contexto: {keywords_str}
        
        Retorne um JSON com:
        - "relevante": true/false
        - "score": número de 0 a 1 (relevância)
        - "contexto_identificado": string explicando o contexto encontrado
        - "palavras_chave_encontradas": lista de palavras-chave identificadas
        
        Seja rigoroso na análise. Apenas considere relevante se realmente falar sobre parentalidade, educação de filhos, família, etc.
        """
    
    @staticmethod
    def prioritize_videos(videos_data: List[Dict]) -> str:
        """Prompt para priorizar vídeos por relevância"""
        videos_info = []
        for i, video in enumerate(videos_data):
            videos_info.append(f"{i+1}. Título: {video['title']} | Views: {video.get('view_count', 0)} | Comentários: {video.get('comment_count', 0)}")
        
        videos_str = "\n".join(videos_info)
        
        return f"""
        Você é um especialista em análise de conteúdo sobre parentalidade.
        
        Analise os vídeos abaixo e ordene-os por ordem de prioridade para análise de comentários.
        Considere: relevância do título, número de views, número de comentários, potencial para gerar discussões sobre parentalidade.
        
        Vídeos:
        {videos_str}
        
        Retorne um JSON com:
        - "ranking": lista de números (posição original) ordenada por prioridade
        - "scores": lista de scores de 0 a 1 para cada vídeo
        - "justificativas": lista de strings explicando a priorização
        
        Exemplo: {{"ranking": [3, 1, 2], "scores": [0.9, 0.7, 0.8], "justificativas": ["...", "...", "..."]}}
        """
    
    @staticmethod
    def classify_comment_tags(comment: str, available_tags: Dict[str, List[str]]) -> str:
        """Prompt para classificar comentários com tags"""
        tags_info = []
        for tag, keywords in available_tags.items():
            tags_info.append(f"- {tag}: {', '.join(keywords)}")
        
        tags_str = "\n".join(tags_info)
        
        return f"""
        Você é um especialista em análise de comentários sobre parentalidade.
        
        Analise o comentário abaixo e identifique quais tags se aplicam baseado nas palavras-chave.
        
        Comentário: "{comment}"
        
        Tags disponíveis e suas palavras-chave:
        {tags_str}
        
        Retorne um JSON com:
        - "tags_encontradas": lista de objetos com "tag", "confianca" (0-1) e "palavras_encontradas"
        
        Exemplo: {{"tags_encontradas": [{{"tag": "filho", "confianca": 0.9, "palavras_encontradas": ["meu filho"]}}]}}
        """
    
    @staticmethod
    def extract_pain_points(comment: str) -> str:
        """Prompt para extrair dores e frustrações"""
        return f"""
        Você é um psicólogo especialista em parentalidade.
        
        Analise o comentário abaixo e extraia as dores, preocupações, medos e frustrações relacionadas à parentalidade.
        
        Comentário: "{comment}"
        
        Retorne um JSON com:
        - "dores_identificadas": lista de strings com as dores encontradas
        - "nivel_intensidade": "baixo", "médio" ou "alto"
        - "categoria_principal": categoria principal da dor (ex: "comportamento", "saúde", "educação")
        - "confianca": score de 0 a 1
        
        Se não encontrar dores relevantes, retorne listas vazias.
        """
    
    @staticmethod
    def extract_desires_dreams(comment: str) -> str:
        """Prompt para extrair desejos e sonhos"""
        return f"""
        Você é um especialista em análise de aspirações parentais.
        
        Analise o comentário abaixo e extraia os desejos, sonhos, objetivos e aspirações relacionadas aos filhos e família.
        
        Comentário: "{comment}"
        
        Retorne um JSON com:
        - "desejos": lista de desejos identificados
        - "sonhos": lista de sonhos/aspirações de longo prazo
        - "objetivos": lista de objetivos específicos
        - "categoria_principal": categoria principal (ex: "educação", "futuro", "bem-estar")
        - "confianca": score de 0 a 1
        
        Se não encontrar desejos/sonhos relevantes, retorne listas vazias.
        """
    
    @staticmethod
    def extract_magic_words(comment: str) -> str:
        """Prompt para extrair palavras mágicas e oportunidades"""
        return f"""
        Você é um especialista em marketing e comunicação para pais.
        
        Analise o comentário abaixo e identifique:
        1. Palavras e expressões que geram engajamento positivo
        2. Oportunidades de negócio ou necessidades não atendidas
        3. Linguagem que ressoa com pais
        
        Comentário: "{comment}"
        
        Retorne um JSON com:
        - "palavras_magicas": lista de palavras/expressões poderosas
        - "oportunidades_negocio": lista de oportunidades identificadas
        - "linguagem_efetiva": padrões de linguagem que funcionam
        - "confianca": score de 0 a 1
        """
    
    @staticmethod
    def generate_persona(analysis_summary: Dict[str, List]) -> str:
        """Prompt para gerar persona final"""
        summary_str = json.dumps(analysis_summary, indent=2, ensure_ascii=False)
        
        return f"""
        Você é um especialista em criação de personas de marketing.
        
        Com base na análise completa de comentários abaixo, crie uma persona detalhada do público-alvo.
        
        Dados da análise:
        {summary_str}
        
        Retorne um JSON detalhado com:
        - "nome_persona": nome fictício representativo
        - "demografia": idade, localização, situação familiar, renda aproximada
        - "dores_principais": top 5 dores mais mencionadas
        - "desejos_principais": top 5 desejos mais comuns
        - "sonhos_aspiracoes": principais sonhos para os filhos
        - "frustracoes_comuns": frustrações mais frequentes
        - "palavras_magicas": palavras que mais geram engajamento
        - "oportunidades_negocio": principais oportunidades identificadas
        - "canais_comunicacao": onde e como se comunicar com essa persona
        - "tom_comunicacao": tom ideal para se comunicar
        - "descricao_narrativa": descrição em formato de história (2-3 parágrafos)
        - "score_confianca": confiança geral na persona (0-1)
        
        Seja específico e baseie-se nos dados fornecidos.
        """
